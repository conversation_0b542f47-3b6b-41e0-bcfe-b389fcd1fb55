#!/usr/bin/env python3
"""
AWS Glue Schema Registry Integration with Kafka Consumer
Simple script to read data from Kafka topics and deserialize using Glue Schema Registry
"""

import json
import logging
from typing import Dict, Any, Optional
from kafka import KafkaConsumer
from aws_glue_schema_registry import GlueSchemaRegistryClient, DataAndSchema
import boto3
from botocore.exceptions import ClientError

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class KafkaGlueSchemaConsumer:
    """
    Kafka consumer with AWS Glue Schema Registry integration
    """
    
    def __init__(
        self,
        kafka_bootstrap_servers: str,
        topic_name: str,
        consumer_group: str,
        aws_region: str = 'us-east-1',
        registry_name: str = 'default-registry'
    ):
        self.kafka_bootstrap_servers = kafka_bootstrap_servers
        self.topic_name = topic_name
        self.consumer_group = consumer_group
        self.aws_region = aws_region
        self.registry_name = registry_name
        
        # Initialize Glue Schema Registry client
        self.glue_client = self._init_glue_client()
        
        # Initialize Kafka consumer
        self.consumer = self._init_kafka_consumer()
    
    def _init_glue_client(self) -> GlueSchemaRegistryClient:
        """Initialize AWS Glue Schema Registry client"""
        try:
            glue_client = GlueSchemaRegistryClient(
                region_name=self.aws_region,
                registry_name=self.registry_name
            )
            logger.info(f"Initialized Glue Schema Registry client for region: {self.aws_region}")
            return glue_client
        except Exception as e:
            logger.error(f"Failed to initialize Glue Schema Registry client: {e}")
            raise
    
    def _init_kafka_consumer(self) -> KafkaConsumer:
        """Initialize Kafka consumer"""
        try:
            consumer = KafkaConsumer(
                self.topic_name,
                bootstrap_servers=self.kafka_bootstrap_servers,
                group_id=self.consumer_group,
                auto_offset_reset='earliest',
                enable_auto_commit=True,
                value_deserializer=lambda x: x,  # Keep raw bytes for schema registry
                key_deserializer=lambda x: x.decode('utf-8') if x else None
            )
            logger.info(f"Initialized Kafka consumer for topic: {self.topic_name}")
            return consumer
        except Exception as e:
            logger.error(f"Failed to initialize Kafka consumer: {e}")
            raise
    
    def deserialize_message(self, message_bytes: bytes) -> Optional[Dict[str, Any]]:
        """
        Deserialize message using Glue Schema Registry
        
        Args:
            message_bytes: Raw message bytes from Kafka
            
        Returns:
            Deserialized message as dictionary or None if deserialization fails
        """
        try:
            # Deserialize using Glue Schema Registry
            data_and_schema: DataAndSchema = self.glue_client.deserialize(message_bytes)
            
            # Extract the actual data
            deserialized_data = data_and_schema.data
            schema_info = data_and_schema.schema
            
            logger.debug(f"Schema version: {schema_info.schema_version_id}")
            logger.debug(f"Schema name: {schema_info.schema_name}")
            
            return deserialized_data
            
        except Exception as e:
            logger.error(f"Failed to deserialize message: {e}")
            return None
    
    def process_message(self, key: str, value: Dict[str, Any]) -> None:
        """
        Process a deserialized message
        
        Args:
            key: Message key
            value: Deserialized message value
        """
        logger.info(f"Processing message with key: {key}")
        logger.info(f"Message content: {json.dumps(value, indent=2)}")
        
        # Add your custom message processing logic here
        # For example:
        # - Store in database
        # - Transform data
        # - Send to another system
        # - Trigger business logic
    
    def consume_messages(self, max_messages: Optional[int] = None) -> None:
        """
        Start consuming messages from Kafka topic
        
        Args:
            max_messages: Maximum number of messages to process (None for infinite)
        """
        logger.info(f"Starting to consume messages from topic: {self.topic_name}")
        
        message_count = 0
        
        try:
            for message in self.consumer:
                # Deserialize the message
                deserialized_value = self.deserialize_message(message.value)
                
                if deserialized_value is not None:
                    # Process the message
                    self.process_message(message.key, deserialized_value)
                    message_count += 1
                    
                    # Check if we've reached the maximum number of messages
                    if max_messages and message_count >= max_messages:
                        logger.info(f"Processed {message_count} messages. Stopping.")
                        break
                else:
                    logger.warning("Skipping message due to deserialization failure")
                    
        except KeyboardInterrupt:
            logger.info("Received interrupt signal. Stopping consumer...")
        except Exception as e:
            logger.error(f"Error while consuming messages: {e}")
        finally:
            self.consumer.close()
            logger.info("Kafka consumer closed")


def create_sample_schema(registry_name: str, schema_name: str, aws_region: str = 'us-east-1') -> None:
    """
    Create a sample Avro schema in Glue Schema Registry
    This is a helper function to set up a test schema
    """
    try:
        glue_client = boto3.client('glue', region_name=aws_region)
        
        # Sample Avro schema for user events
        sample_schema = {
            "type": "record",
            "name": "UserEvent",
            "fields": [
                {"name": "user_id", "type": "string"},
                {"name": "event_type", "type": "string"},
                {"name": "timestamp", "type": "long"},
                {"name": "properties", "type": {"type": "map", "values": "string"}}
            ]
        }
        
        response = glue_client.create_schema(
            RegistryId={'RegistryName': registry_name},
            SchemaName=schema_name,
            DataFormat='AVRO',
            SchemaDefinition=json.dumps(sample_schema),
            Description='Sample schema for user events'
        )
        
        logger.info(f"Created schema: {schema_name} with ARN: {response['SchemaArn']}")
        
    except ClientError as e:
        if e.response['Error']['Code'] == 'AlreadyExistsException':
            logger.info(f"Schema {schema_name} already exists")
        else:
            logger.error(f"Failed to create schema: {e}")
            raise


def main():
    """
    Main function to demonstrate Kafka + Glue Schema Registry integration
    """
    # Configuration
    KAFKA_BOOTSTRAP_SERVERS = 'localhost:9092'  # Update with your Kafka brokers
    TOPIC_NAME = 'user-events'
    CONSUMER_GROUP = 'glue-schema-consumer-group'
    AWS_REGION = 'us-east-1'
    REGISTRY_NAME = 'sample-registry'
    SCHEMA_NAME = 'user-event-schema'
    
    try:
        # Optional: Create a sample schema (uncomment if needed)
        # create_sample_schema(REGISTRY_NAME, SCHEMA_NAME, AWS_REGION)
        
        # Initialize and start the consumer
        consumer = KafkaGlueSchemaConsumer(
            kafka_bootstrap_servers=KAFKA_BOOTSTRAP_SERVERS,
            topic_name=TOPIC_NAME,
            consumer_group=CONSUMER_GROUP,
            aws_region=AWS_REGION,
            registry_name=REGISTRY_NAME
        )
        
        # Start consuming messages (process 10 messages for demo)
        consumer.consume_messages(max_messages=10)
        
    except Exception as e:
        logger.error(f"Application error: {e}")
        raise


if __name__ == "__main__":
    main()
