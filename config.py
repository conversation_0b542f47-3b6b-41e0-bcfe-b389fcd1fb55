"""
Configuration settings for Kafka + Glue Schema Registry integration
"""

import os
from typing import Dict, Any

# Kafka Configuration
KAFKA_CONFIG = {
    'bootstrap_servers': os.getenv('KAFKA_BOOTSTRAP_SERVERS', 'localhost:9092'),
    'topic_name': os.getenv('KAFKA_TOPIC_NAME', 'user-events'),
    'consumer_group': os.getenv('KAFKA_CONSUMER_GROUP', 'glue-schema-consumer-group'),
}

# AWS Configuration
AWS_CONFIG = {
    'region': os.getenv('AWS_REGION', 'us-east-1'),
    'registry_name': os.getenv('GLUE_REGISTRY_NAME', 'sample-registry'),
    'schema_name': os.getenv('GLUE_SCHEMA_NAME', 'user-event-schema'),
}

# Application Configuration
APP_CONFIG = {
    'max_messages': int(os.getenv('MAX_MESSAGES', '10')),
    'log_level': os.getenv('LOG_LEVEL', 'INFO'),
}

def get_config() -> Dict[str, Any]:
    """Get complete configuration"""
    return {
        'kafka': KAFKA_CONFIG,
        'aws': AWS_CONFIG,
        'app': APP_CONFIG,
    }
