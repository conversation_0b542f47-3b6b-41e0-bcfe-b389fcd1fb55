#!/usr/bin/env python3
"""
Sample Kafka producer with AWS Glue Schema Registry serialization
This script demonstrates how to produce messages to Kafka using Glue Schema Registry
"""

import json
import time
import logging
from typing import Dict, Any
from kafka import KafkaProducer
from aws_glue_schema_registry import GlueSchemaRegistryClient
import uuid
from datetime import datetime

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class KafkaGlueSchemaProducer:
    """
    Kafka producer with AWS Glue Schema Registry integration
    """
    
    def __init__(
        self,
        kafka_bootstrap_servers: str,
        topic_name: str,
        aws_region: str = 'us-east-1',
        registry_name: str = 'default-registry',
        schema_name: str = 'user-event-schema'
    ):
        self.kafka_bootstrap_servers = kafka_bootstrap_servers
        self.topic_name = topic_name
        self.aws_region = aws_region
        self.registry_name = registry_name
        self.schema_name = schema_name
        
        # Initialize Glue Schema Registry client
        self.glue_client = self._init_glue_client()
        
        # Initialize Kafka producer
        self.producer = self._init_kafka_producer()
    
    def _init_glue_client(self) -> GlueSchemaRegistryClient:
        """Initialize AWS Glue Schema Registry client"""
        try:
            glue_client = GlueSchemaRegistryClient(
                region_name=self.aws_region,
                registry_name=self.registry_name
            )
            logger.info(f"Initialized Glue Schema Registry client for region: {self.aws_region}")
            return glue_client
        except Exception as e:
            logger.error(f"Failed to initialize Glue Schema Registry client: {e}")
            raise
    
    def _init_kafka_producer(self) -> KafkaProducer:
        """Initialize Kafka producer"""
        try:
            producer = KafkaProducer(
                bootstrap_servers=self.kafka_bootstrap_servers,
                key_serializer=lambda x: x.encode('utf-8') if x else None,
                value_serializer=lambda x: x  # We'll serialize using Glue Schema Registry
            )
            logger.info(f"Initialized Kafka producer for topic: {self.topic_name}")
            return producer
        except Exception as e:
            logger.error(f"Failed to initialize Kafka producer: {e}")
            raise
    
    def serialize_message(self, data: Dict[str, Any]) -> bytes:
        """
        Serialize message using Glue Schema Registry
        
        Args:
            data: Dictionary containing the message data
            
        Returns:
            Serialized message bytes
        """
        try:
            # Serialize using Glue Schema Registry
            serialized_data = self.glue_client.serialize(
                data=data,
                schema_name=self.schema_name
            )
            
            logger.debug(f"Serialized message with schema: {self.schema_name}")
            return serialized_data
            
        except Exception as e:
            logger.error(f"Failed to serialize message: {e}")
            raise
    
    def send_message(self, key: str, data: Dict[str, Any]) -> None:
        """
        Send a message to Kafka topic
        
        Args:
            key: Message key
            data: Message data to serialize and send
        """
        try:
            # Serialize the message
            serialized_data = self.serialize_message(data)
            
            # Send to Kafka
            future = self.producer.send(
                self.topic_name,
                key=key,
                value=serialized_data
            )
            
            # Wait for the message to be sent
            record_metadata = future.get(timeout=10)
            
            logger.info(f"Message sent successfully:")
            logger.info(f"  Topic: {record_metadata.topic}")
            logger.info(f"  Partition: {record_metadata.partition}")
            logger.info(f"  Offset: {record_metadata.offset}")
            logger.info(f"  Key: {key}")
            
        except Exception as e:
            logger.error(f"Failed to send message: {e}")
            raise
    
    def generate_sample_data(self) -> Dict[str, Any]:
        """Generate sample user event data"""
        event_types = ['login', 'logout', 'purchase', 'view_product', 'add_to_cart']
        
        return {
            'user_id': str(uuid.uuid4()),
            'event_type': event_types[int(time.time()) % len(event_types)],
            'timestamp': int(datetime.now().timestamp() * 1000),  # milliseconds
            'properties': {
                'session_id': str(uuid.uuid4()),
                'user_agent': 'Mozilla/5.0 (compatible; SampleBot/1.0)',
                'ip_address': '*************'
            }
        }
    
    def produce_sample_messages(self, count: int = 5) -> None:
        """
        Produce sample messages to Kafka
        
        Args:
            count: Number of messages to produce
        """
        logger.info(f"Producing {count} sample messages to topic: {self.topic_name}")
        
        try:
            for i in range(count):
                # Generate sample data
                sample_data = self.generate_sample_data()
                message_key = f"user-event-{i+1}"
                
                # Send the message
                self.send_message(message_key, sample_data)
                
                # Small delay between messages
                time.sleep(1)
                
            # Ensure all messages are sent
            self.producer.flush()
            logger.info(f"Successfully produced {count} messages")
            
        except Exception as e:
            logger.error(f"Error producing messages: {e}")
            raise
        finally:
            self.producer.close()
            logger.info("Kafka producer closed")


def main():
    """
    Main function to demonstrate message production
    """
    # Configuration
    KAFKA_BOOTSTRAP_SERVERS = 'localhost:9092'
    TOPIC_NAME = 'user-events'
    AWS_REGION = 'us-east-1'
    REGISTRY_NAME = 'sample-registry'
    SCHEMA_NAME = 'user-event-schema'
    
    try:
        # Initialize and start the producer
        producer = KafkaGlueSchemaProducer(
            kafka_bootstrap_servers=KAFKA_BOOTSTRAP_SERVERS,
            topic_name=TOPIC_NAME,
            aws_region=AWS_REGION,
            registry_name=REGISTRY_NAME,
            schema_name=SCHEMA_NAME
        )
        
        # Produce sample messages
        producer.produce_sample_messages(count=5)
        
    except Exception as e:
        logger.error(f"Application error: {e}")
        raise


if __name__ == "__main__":
    main()
