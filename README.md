# Kafka + AWS Glue Schema Registry Integration Sample

This project demonstrates how to integrate Apache Kafka with AWS Glue Schema Registry for schema-based message serialization and deserialization.

## Features

- **Schema-based serialization/deserialization** using AWS Glue Schema Registry
- **Kafka consumer** that reads and deserializes messages
- **Kafka producer** that serializes and sends messages
- **Simple configuration** management
- **Comprehensive logging** for debugging
- **Error handling** and graceful shutdown

## Prerequisites

1. **AWS Account** with Glue Schema Registry access
2. **Apache Kafka** cluster (local or remote)
3. **Python 3.7+**
4. **AWS credentials** configured (via AWS CLI, environment variables, or IAM roles)

## Setup

### 1. Install Dependencies

```bash
pip install -r requirements.txt
```

### 2. Configure AWS Credentials

Ensure your AWS credentials are configured. You can use:

- AWS CLI: `aws configure`
- Environment variables: `AWS_ACCESS_KEY_ID`, `AWS_SECRET_ACCESS_KEY`
- IAM roles (if running on EC2)

### 3. Set Up Kafka

If you don't have Kafka running locally, you can use Docker:

```bash
# Start Kafka with Docker Compose
docker-compose up -d

# Or use the following commands for a quick setup:
docker run -d --name zookeeper -p 2181:2181 confluentinc/cp-zookeeper:latest
docker run -d --name kafka -p 9092:9092 --link zookeeper \
  -e KAFKA_ZOOKEEPER_CONNECT=zookeeper:2181 \
  -e KAFKA_ADVERTISED_LISTENERS=PLAINTEXT://localhost:9092 \
  confluentinc/cp-kafka:latest
```

### 4. Create Schema Registry and Schema

The sample includes a helper function to create a sample schema. You can also create it manually:

```python
from kafka_glue_schema_registry_sample import create_sample_schema

create_sample_schema(
    registry_name='sample-registry',
    schema_name='user-event-schema',
    aws_region='us-east-1'
)
```

## Usage

### Running the Consumer

The consumer reads messages from Kafka and deserializes them using Glue Schema Registry:

```bash
python kafka_glue_schema_registry_sample.py
```

### Running the Producer (for testing)

To generate test messages:

```bash
python kafka_producer_sample.py
```

### Configuration

You can configure the application using environment variables:

```bash
export KAFKA_BOOTSTRAP_SERVERS="localhost:9092"
export KAFKA_TOPIC_NAME="user-events"
export KAFKA_CONSUMER_GROUP="glue-schema-consumer-group"
export AWS_REGION="us-east-1"
export GLUE_REGISTRY_NAME="sample-registry"
export GLUE_SCHEMA_NAME="user-event-schema"
export MAX_MESSAGES="10"
export LOG_LEVEL="INFO"
```

## Sample Schema

The sample uses an Avro schema for user events:

```json
{
  "type": "record",
  "name": "UserEvent",
  "fields": [
    {"name": "user_id", "type": "string"},
    {"name": "event_type", "type": "string"},
    {"name": "timestamp", "type": "long"},
    {"name": "properties", "type": {"type": "map", "values": "string"}}
  ]
}
```

## Key Components

### KafkaGlueSchemaConsumer

- Initializes Kafka consumer and Glue Schema Registry client
- Deserializes messages using the schema registry
- Processes messages with custom business logic

### KafkaGlueSchemaProducer

- Serializes messages using Glue Schema Registry
- Sends messages to Kafka topics
- Generates sample test data

## Error Handling

The sample includes comprehensive error handling for:

- AWS authentication issues
- Kafka connection problems
- Schema registry errors
- Message serialization/deserialization failures

## Customization

To adapt this sample for your use case:

1. **Update the schema** in `create_sample_schema()` function
2. **Modify message processing** in `process_message()` method
3. **Adjust configuration** in `config.py`
4. **Add custom business logic** as needed

## Troubleshooting

### Common Issues

1. **AWS Credentials**: Ensure AWS credentials are properly configured
2. **Kafka Connection**: Verify Kafka is running and accessible
3. **Schema Registry**: Check that the registry and schema exist in AWS
4. **Network**: Ensure network connectivity to both Kafka and AWS

### Debugging

Enable debug logging:

```bash
export LOG_LEVEL="DEBUG"
```

## License

This sample code is provided for educational purposes. Modify as needed for your use case.
